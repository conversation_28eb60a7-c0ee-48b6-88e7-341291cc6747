/**
 * Sprite Sheet Generator for Animation Lab
 * Generates 5x5 sprite sheets for animated symbols using OpenAI API
 */

import { enhancedOpenaiClient } from './enhancedOpenaiClient';

export interface SpriteSheetConfig {
  prompt: string;
  symbolType: 'block' | 'contour';
  contentType: 'symbol-only' | 'symbol-wild' | 'symbol-scatter' | 'symbol-bonus' | 'symbol-free' | 'symbol-jackpot' | 'text-only';
  animationComplexity: 'simple' | 'medium' | 'complex';
  layoutTemplate?: 'text-top' | 'text-bottom' | 'text-overlay';
}

export interface SpriteSheetResult {
  success: boolean;
  spriteSheetUrl?: string;
  error?: string;
}

// ───────────────────────────────────────────────────
// Horizontal strip configuration matching gem sprite sheet
const COLS         = 32;  // 32 frames like the gem image
const ROWS         = 1;   // Single row horizontal strip
const SYMBOL_SIZE  = 48;  // Small square frames for very wide layout
const GUTTER       = 0;   // No gaps for seamless cutting
const OUTER_MARGIN = 0;   // No margins for precise cutting

// Derived:
const SPRITE_WIDTH  = SYMBOL_SIZE * COLS;  // 1536px wide
const SPRITE_HEIGHT = SYMBOL_SIZE * ROWS;  // 48px tall
// ───────────────────────────────────────────────────


/**
 * Generate animation prompts based on complexity level
 */
const getAnimationDescription = (complexity: 'simple' | 'medium' | 'complex'): string => {
  const descriptions = {
    simple: 'gentle floating motion, subtle glow variations, soft breathing effect',
    medium: 'dynamic movement with rotation, pulsing energy, color shifts, particle effects',
    complex: 'elaborate transformation sequence, magical effects, multiple animation layers, dramatic lighting changes'
  };
  return descriptions[complexity];
};

/**
 * Generate content-specific animation details
 */
const getContentAnimationDetails = (contentType: string): string => {
  const contentAnimations = {
    'symbol-wild': 'WILD text glowing and pulsing, symbol radiating energy',
    'symbol-scatter': 'SCATTER text sparkling, symbol with magical aura',
    'symbol-bonus': 'BONUS text bouncing, symbol with celebration effects',
    'symbol-free': 'FREE text floating, symbol with liberation effects',
    'symbol-jackpot': 'JACKPOT text shimmering, symbol with golden rays',
    'symbol-only': 'symbol with natural movement and energy',
    'text-only': 'text with dynamic typography effects'
  };
  return contentAnimations[contentType] || 'symbol with smooth animation';
};

/**
 * Generate a horizontal strip sprite sheet for animation
 */
export const generateSpriteSheet = async (config: SpriteSheetConfig): Promise<SpriteSheetResult> => {
  try {
    console.log('🎬 Generating horizontal strip sprite sheet for animation...');
    
    const animationDesc = getAnimationDescription(config.animationComplexity);
    const contentAnimationDesc = getContentAnimationDetails(config.contentType);
    
    // Create enhanced prompt for 32-frame horizontal strip sprite sheet
    const spriteSheetPrompt = `
Create a PRECISE 5x5 grid sprite sheet (25 frames total) for slot machine symbol animation with PROPER SPACING: and it's important to generate 5x5 layout

CRITICAL GRID REQUIREMENTS WITH SPACING:
- EXACT 5x5 grid layout 
- Frame layout: 5 columns × 5 rows with no spacing throughout
- IMPORTANT: Leave 16px margin from all edges to prevent cutting
- no frame boundary should be defined just keep this transparent
- IMPORTANT: Do not add any border to the frames Keep them transparent

FRAME DISTRIBUTION CALCULATION:
- Total canvas: 1024x1024 pixels
- Fix Margin from all edges: 16px on all sides of the canvas (not more then this and not less then this)
- Usable area: 992x992 pixels
- Frame size including spacing: 198.4x198.4 pixels per frame slot but do not add any border to the frame 
- Actual symbol area per frame: The Actual Symbol area should use fixed 180x180 pixels(not more then this and not less then this) should be in center of frame the symbol should not exceeds 180x180px 

SYMBOL DESCRIPTION: ${config.prompt}

BACKGROUND REQUIREMENTS:
- COMPLETELY TRANSPARENT background for all frames and it's important that the background should be transparent
- NO solid colors, NO gradients in background areas just transparent
- Only the symbol/text should be visible, everything else transparent
- Use PNG format with alpha channel transparency
- Background must be 100% transparent (alpha = 0)


CRITICAL BOTTOM ROW REQUIREMENTS:

- NEVER cut off or crop the bottom row - ensure full 180px height for all bottom frames

- Professional game art quality with clean, sharp edges
- NO background elements, NO environmental effects
- Focus only on the symbol/text animation

ANIMATION CONTINUITY:
- Frame 25 should smoothly connect back to frame 1 for seamless looping
- Maintain consistent lighting and color palette across all frames
- Smooth progression between adjacent frames
- NEVER change symbol size or position - only animate effects, glow, rotation, etc.

Style: Professional slot machine game art, vibrant colors, transparent background, pixel-perfect grid alignment, high quality digital illustration optimized for sprite sheet cutting.
`;

    console.log('🎨 32-frame horizontal strip prompt:', spriteSheetPrompt.substring(0, 200) + '...');

    // Generate the sprite sheet using OpenAI with optimal settings
    const response = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: spriteSheetPrompt,
      targetSymbolId: `spritesheet_${Date.now()}`,
      gameId: 'animation-lab-sprites'
    });

    if (response.success && response.images && response.images.length > 0) {
      console.log('✅ Horizontal strip sprite sheet generated successfully');
      return {
        success: true,
        spriteSheetUrl: response.images[0]
      };
    } else {
      throw new Error(response.error || 'Failed to generate horizontal strip sprite sheet');
    }

  } catch (error) {
    console.error('❌ Horizontal strip generation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Extract individual frames from a horizontal strip sprite sheet
 * This function will be used by the PixiJS animation system
 */
export const extractFramesFromSpriteSheet = (
  spriteSheetTexture: any, // PIXI.Texture
  frameWidth: number,
  frameHeight: number
): any[] => {
  const frames = [];

  // Extract 32 frames from horizontal strip (32x1)
  for (let col = 0; col < 32; col++) {
    const frameTexture = new (window as any).PIXI.Texture(
      spriteSheetTexture,
      new (window as any).PIXI.Rectangle(
        col * frameWidth,
        0, // Single row, so Y is always 0
        frameWidth,
        frameHeight
      )
    );
    frames.push(frameTexture);
  }

  return frames;
};
