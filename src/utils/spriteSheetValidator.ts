/**
 * Sprite Sheet Validation and Cutting Utilities
 * Ensures proper sprite sheet format and provides precise cutting logic
 */

export interface SpriteSheetValidationResult {
  isValid: boolean;
  dimensions: { width: number; height: number; frameWidth: number; frameHeight: number; };
  gridInfo: { cols: number; rows: number; totalFrames: number; };
  issues: string[];
  recommendations: string[];
}

export interface SpriteFrame {
  x: number; y: number; width: number; height: number;
  frameIndex: number; row: number; col: number;
}
// ───────────────────────────────────────────────────
const COLS         = 5;
const ROWS         = 5;
const SYMBOL_SIZE  = 180;  // px of actual symbol in each frame
const GUTTER       = 0;   // px between each frame
const OUTER_MARGIN = 16;   // px around all edges

// Derived:
const CELL_SIZE    = SYMBOL_SIZE + GUTTER;  
const SPRITE_WIDTH  = SYMBOL_SIZE*COLS + GUTTER*(COLS-1) + OUTER_MARGIN*2;
const SPRITE_HEIGHT = SYMBOL_SIZE*ROWS + GUTTER*(ROWS-1) + OUTER_MARGIN*2;
// ───────────────────────────────────────────────────


/**
 * Validate sprite sheet dimensions and grid alignment
 */
export const validateSpriteSheet = (
  imageWidth: number,
  imageHeight: number,
  gridCols = COLS,
  gridRows = ROWS
): SpriteSheetValidationResult => {
  const issues: string[] = [];
  const recs: string[] = [];

  if (imageWidth !== SPRITE_WIDTH || imageHeight !== SPRITE_HEIGHT) {
    issues.push(
      `Invalid sheet size ${imageWidth}×${imageHeight}; expected ${SPRITE_WIDTH}×${SPRITE_HEIGHT}.`
    );
  }

  const cellWidth  = SYMBOL_SIZE;
  const cellHeight = SYMBOL_SIZE;
  const frameW = cellWidth;
  const frameH = cellHeight;

  return {
    isValid: issues.length === 0,
    dimensions: { width: imageWidth, height: imageHeight, frameWidth: frameW, frameHeight: frameH },
    gridInfo: { cols: gridCols, rows: gridRows, totalFrames: gridCols * gridRows },
    issues,
    recommendations: recs
  };
};

export const validateSpriteSheet1 = (
  imageWidth: number,
  imageHeight: number,
  gridCols = COLS,
  gridRows = ROWS
): SpriteSheetValidationResult => {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Only accept exactly the dimensions we asked the AI to produce:
  if (imageWidth !== SPRITE_WIDTH || imageHeight !== SPRITE_HEIGHT) {
    issues.push(
      `Unexpected sprite size: ${imageWidth}×${imageHeight}. ` +
      `Expected exactly ${SPRITE_WIDTH}×${SPRITE_HEIGHT}.`
    );
  }

  return {
    isValid: issues.length === 0,
    dimensions: {
      width: imageWidth,
      height: imageHeight,
      frameWidth: SYMBOL_SIZE,
      frameHeight: SYMBOL_SIZE
    },
    gridInfo: { cols: gridCols, rows: gridRows, totalFrames: gridCols * gridRows },
    issues,
    recommendations
  };
};


/**
 * Generate precise frame coordinates for sprite cutting with proper spacing
 */
export const generateFrameCoordinates = (
  imageWidth: number,
  imageHeight: number,
  gridCols = COLS,
  gridRows = ROWS
): SpriteFrame[] => {
  const frames: SpriteFrame[] = [];
  let index = 0;

  for (let row = 0; row < gridRows; row++) {
    for (let col = 0; col < gridCols; col++) {
      const x = OUTER_MARGIN + col * (SYMBOL_SIZE + GUTTER);
      const y = OUTER_MARGIN + row * (SYMBOL_SIZE + GUTTER);

      frames.push({
        x, y,
        width: SYMBOL_SIZE,
        height: SYMBOL_SIZE,
        frameIndex: index,
        row, col
      });
      index++;
    }
  }
  return frames;
};


/**
 * Analyze sprite sheet for common issues
 */
export const analyzeSpriteSheet = async (
  imageUrl: string, 
  gridCols: number = 5, 
  gridRows: number = 5
): Promise<{
  analysis: SpriteSheetValidationResult;
  frames: SpriteFrame[];
  debugInfo: {
    loadTime: number;
    imageFormat: string;
    hasTransparency: boolean;
  };
}> => {
  const startTime = Date.now();
  
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      const loadTime = Date.now() - startTime;
      
      // Create canvas to analyze the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Could not create canvas context'));
        return;
      }
      
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      
      // Check for transparency
      const imageData = ctx.getImageData(0, 0, img.width, img.height);
      let hasTransparency = false;
      
      for (let i = 3; i < imageData.data.length; i += 4) {
        if (imageData.data[i] < 255) {
          hasTransparency = true;
          break;
        }
      }
      
      // Validate sprite sheet with custom grid
      const analysis = validateSpriteSheet(img.width, img.height, gridCols, gridRows);
      const frames = generateFrameCoordinates(img.width, img.height, gridCols, gridRows);
      
      resolve({
        analysis,
        frames,
        debugInfo: {
          loadTime,
          imageFormat: imageUrl.includes('.png') ? 'PNG' : 
                      imageUrl.includes('.jpg') || imageUrl.includes('.jpeg') ? 'JPEG' : 'Unknown',
          hasTransparency
        }
      });
    };
    
    img.onerror = () => {
      reject(new Error(`Failed to load image: ${imageUrl}`));
    };
    
    img.src = imageUrl;
  });
};

/**
 * Generate debug visualization of sprite sheet cutting
 */
export const generateDebugVisualization = (
  frames: SpriteFrame[],
  imageWidth: number,
  imageHeight: number
): string => {
  let visualization = `Sprite Sheet Debug (${imageWidth}x${imageHeight}):\n`;
  visualization += `${'─'.repeat(50)}\n`;
  
  frames.forEach((frame, index) => {
    visualization += `Frame ${index.toString().padStart(2, '0')}: `;
    visualization += `(${frame.x.toString().padStart(3, ' ')},${frame.y.toString().padStart(3, ' ')}) `;
    visualization += `${frame.width}x${frame.height} `;
    visualization += `[Row ${frame.row}, Col ${frame.col}]\n`;
  });
  
  return visualization;
};

/**
 * Recommended sprite sheet generation settings
 */
export const SPRITE_SHEET_RECOMMENDATIONS = {
  // Optimal dimensions for 5x5 grid
  OPTIMAL_SIZES: [
    { width: 1000, height: 1000, frameSize: 200 },
    { width: 1024, height: 1024, frameSize: 204.8 },
    { width: 1280, height: 1280, frameSize: 256 },
    { width: 1600, height: 1600, frameSize: 320 }
  ],
  
  // Grid configurations
  GRID_CONFIGS: [
    { cols: 5, rows: 5, frames: 25, name: 'Standard 5x5' },
    { cols: 4, rows: 4, frames: 16, name: 'Compact 4x4' },
    { cols: 6, rows: 6, frames: 36, name: 'Extended 6x6' },
    { cols: 8, rows: 4, frames: 32, name: 'Wide 8x4' }
  ],
  
  // Quality settings
  QUALITY_SETTINGS: {
    MIN_FRAME_SIZE: 64,
    RECOMMENDED_FRAME_SIZE: 200,
    MAX_FRAME_SIZE: 512,
    PREFERRED_FORMAT: 'PNG',
    TRANSPARENCY_REQUIRED: true
  }
};
